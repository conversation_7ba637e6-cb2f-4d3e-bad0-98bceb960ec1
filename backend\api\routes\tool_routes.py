"""
工具相关路由处理模块
"""
import json
import logging
from typing import Dict, List, Any
import csv
import xml.etree.ElementTree as ET
import configparser
import io

from fastapi import APIRouter, HTTPException, UploadFile, File
import os
import tempfile

from docx import Document
from pptx import Presentation
import openpyxl
# import fitz  # 改为延迟导入
import xlrd

# 尝试导入可选依赖
try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False

# 从其他模块导入
from backend.mcp.server_manager import mcp_server

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

@router.post("/call-tool")
async def call_tool(data: Dict[str, Any]):
    """
    执行工具调用
    """
    try:
        name = data.get("name")
        arguments = data.get("arguments", {})
        
        if not name:
            raise HTTPException(status_code=400, detail="工具名称不能为空")
        
        # 尝试解析参数(如果是字符串)
        if isinstance(arguments, str):
            try:
                arguments = json.loads(arguments)
            except:
                pass
        
        result = await mcp_server.call_tool(name, arguments)
        return result
    except Exception as e:
        logger.error(f"工具调用出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list-tools")
async def list_tools():
    """
    获取可用工具列表
    """
    try:
        tools = await mcp_server.list_tools()
        return {"tools": tools}
    except Exception as e:
        logger.error(f"获取工具列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/list-tools-from-servers")
async def list_tools_from_servers(data: Dict[str, Any]):
    """
    从指定的MCP服务器获取工具列表
    """
    try:
        server_names = data.get("servers", [])
        if not server_names:
            return {"tools": []}
            
        # 确保 server_names 是字符串列表
        if not isinstance(server_names, list):
            logger.error(f"服务器参数必须是列表: {server_names}")
            raise HTTPException(status_code=400, detail="服务器参数必须是列表")
            
        # 过滤掉非字符串的服务器名称
        valid_server_names = []
        for server in server_names:
            if isinstance(server, str):
                valid_server_names.append(server)
            else:
                logger.warning(f"忽略非字符串服务器名称: {server}")
                
        if not valid_server_names:
            logger.warning("没有有效的服务器名称")
            return {"tools": []}
            
        tools = await mcp_server.list_tools_from_servers(valid_server_names)
        return {"tools": tools}
    except Exception as e:
        logger.error(f"从指定服务器获取工具列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload-and-parse-file")
async def upload_and_parse_file(file: UploadFile = File(...)):
    """
    上传并解析office文件为文本
    """
    try:
        filename = file.filename
        ext = os.path.splitext(filename)[-1].lower().replace('.', '')
        # 用 mkstemp 创建临时文件
        fd, tmp_path = tempfile.mkstemp(suffix='.'+ext)
        try:
            with os.fdopen(fd, 'wb') as tmp:
                content = await file.read()
                tmp.write(content)

            text = ""
            if ext == "docx":
                doc = Document(tmp_path)
                text = "\n".join([p.text for p in doc.paragraphs])
            elif ext == "pptx":
                prs = Presentation(tmp_path)
                slides = []
                for slide in prs.slides:
                    for shape in slide.shapes:
                        if hasattr(shape, "text"):
                            slides.append(shape.text)
                text = "\n".join(slides)
            elif ext == "xlsx":
                wb = openpyxl.load_workbook(tmp_path, read_only=True)
                try:
                    sheets = []
                    for ws in wb.worksheets:
                        for row in ws.iter_rows(values_only=True):
                            sheets.append("\t".join([str(cell) if cell is not None else "" for cell in row]))
                    text = "\n".join(sheets)
                finally:
                    wb.close()
            elif ext == "pdf":
                # 延迟导入 fitz，避免初始化时的静态文件问题
                import fitz
                doc = fitz.open(tmp_path)
                pdf_texts = []
                for page_num in range(doc.page_count):
                    page = doc.load_page(page_num)
                    pdf_texts.append(page.get_text())
                doc.close()
                text = "\n".join(pdf_texts)
            elif ext == "xls":
                workbook = xlrd.open_workbook(tmp_path)
                xls_texts = []
                for sheet_name in workbook.sheet_names():
                    sheet = workbook.sheet_by_name(sheet_name)
                    for row_idx in range(sheet.nrows):
                        row_values = [str(cell.value) for cell in sheet.row(row_idx)]
                        xls_texts.append("\t".join(row_values))
                text = "\n".join(xls_texts)
            elif ext == "txt":
                # 处理txt文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    text = f.read()
            elif ext == "md":
                # 处理markdown文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    text = f.read()
            # 数据格式文件
            elif ext == "csv":
                # 处理CSV文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    csv_reader = csv.reader(f)
                    rows = []
                    for row in csv_reader:
                        rows.append('\t'.join(row))
                    text = '\n'.join(rows)
            elif ext == "json":
                # 处理JSON文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    try:
                        data = json.load(f)
                        text = json.dumps(data, indent=2, ensure_ascii=False)
                    except json.JSONDecodeError:
                        # 如果JSON格式错误，直接读取原文本
                        f.seek(0)
                        text = f.read()
            elif ext == "xml":
                # 处理XML文件
                try:
                    tree = ET.parse(tmp_path)
                    root = tree.getroot()
                    # 提取所有文本内容
                    text = ET.tostring(root, encoding='unicode', method='text')
                except ET.ParseError:
                    # 如果XML格式错误，直接读取原文本
                    with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()
            elif ext == "html":
                # 处理HTML文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    html_content = f.read()
                    if HAS_BS4:
                        soup = BeautifulSoup(html_content, 'html.parser')
                        text = soup.get_text(separator='\n', strip=True)
                    else:
                        # 如果没有BeautifulSoup，直接返回原文本
                        text = html_content
            # 代码文件
            elif ext in ["py", "js", "java", "cpp", "c", "h", "css", "sql", "php", "rb", "go", "rs", "swift", "kt", "ts", "jsx", "tsx", "vue", "svelte"]:
                # 处理各种代码文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    text = f.read()
            # 配置文件
            elif ext in ["yaml", "yml"]:
                # 处理YAML文件
                with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                    if HAS_YAML:
                        try:
                            data = yaml.safe_load(f)
                            text = yaml.dump(data, default_flow_style=False, allow_unicode=True)
                        except yaml.YAMLError:
                            # 如果YAML格式错误，直接读取原文本
                            f.seek(0)
                            text = f.read()
                    else:
                        # 如果没有yaml库，直接读取原文本
                        text = f.read()
            elif ext in ["ini", "cfg", "conf"]:
                # 处理配置文件
                try:
                    config = configparser.ConfigParser()
                    config.read(tmp_path, encoding='utf-8')
                    
                    # 将配置转换为可读文本
                    text_parts = []
                    for section_name in config.sections():
                        text_parts.append(f"[{section_name}]")
                        for key, value in config.items(section_name):
                            text_parts.append(f"{key} = {value}")
                        text_parts.append("")  # 空行分隔
                    text = '\n'.join(text_parts)
                except configparser.Error:
                    # 如果配置文件格式错误，直接读取原文本
                    with open(tmp_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()
            # TODO: 其他格式如doc/rtf/odt/epub可补充
            else:
                return {"success": False, "error": f"暂不支持该文件类型: {ext}"}
        finally:
            os.remove(tmp_path)
        return {
            "success": True,
            "filename": filename,
            "filetype": ext,
            "text": text
        }
    except Exception as e:
        if "Package not found" in str(e):
            return {"success": False, "error": "文件为空！"} 
        else:
            return {"success": False, "error": "文件解析失败:"+str(e)} 